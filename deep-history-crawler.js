// 深度历史帖子爬虫 - 获取更多历史帖子
const puppeteer = require('puppeteer');
const fs = require('fs-extra');

class DeepHistoryCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
    this.oldestPostDate = null;
  }

  async init() {
    console.log('🚀 启动深度历史爬虫...');
    
    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        console.log(`✅ 找到Chrome: ${path}`);
        break;
      }
    }

    const launchOptions = {
      headless: false,
      slowMo: 30,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }
    
    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
    console.log('✅ 浏览器启动成功');
  }

  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  async checkLogin() {
    console.log('🔍 检查登录状态...');
    
    await this.page.goto('https://sns.vip.stockstar.com/sns', { 
      waitUntil: 'networkidle2' 
    });

    const isLoggedIn = await this.page.evaluate(() => {
      return !document.body.textContent.includes('登录') || 
             document.body.textContent.includes('退出');
    });

    if (!isLoggedIn) {
      console.log('🔐 需要登录，请在浏览器中手动登录...');
      console.log('登录完成后，按回车键继续...');
      
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve());
      });

      const cookies = await this.page.cookies();
      await fs.ensureDir('./cookies');
      await fs.writeJson('./cookies/cookies.json', cookies);
      console.log('✅ 登录状态已保存');
    } else {
      console.log('✅ 已登录状态');
    }

    return true;
  }

  // 提取帖子并分析时间信息
  async extractPostsWithTime() {
    const posts = await this.page.evaluate(() => {
      const results = [];
      const links = document.querySelectorAll('a[href*="forumdetail"]');
      
      links.forEach(link => {
        const title = link.textContent.trim();
        const url = link.href;
        
        if (title && title.length > 3 && url) {
          // 尝试从URL中提取帖子ID
          const idMatch = url.match(/forumdetail\/(\d+)/);
          const postId = idMatch ? parseInt(idMatch[1]) : null;
          
          results.push({ 
            title, 
            url, 
            postId,
            // 尝试从周围元素获取时间信息
            timeInfo: null
          });
        }
      });

      return results;
    });

    let newPostsCount = 0;
    let oldestId = null;
    
    posts.forEach(post => {
      if (!this.seenUrls.has(post.url)) {
        this.seenUrls.add(post.url);
        this.allPosts.push({
          id: this.allPosts.length + 1,
          title: post.title,
          url: post.url,
          postId: post.postId
        });
        newPostsCount++;
        
        // 跟踪最小的帖子ID（通常对应最老的帖子）
        if (post.postId && (!oldestId || post.postId < oldestId)) {
          oldestId = post.postId;
        }
      }
    });

    if (oldestId) {
      console.log(`📅 当前最老帖子ID: ${oldestId}`);
    }

    return newPostsCount;
  }

  // 超级激进的滚动策略
  async performSuperAggressiveScroll() {
    try {
      console.log('🚀 执行超级激进滚动...');
      
      const viewport = await this.page.viewport();
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      await this.page.mouse.move(centerX, centerY);
      
      // 第一阶段：快速大量滚动
      for (let round = 0; round < 3; round++) {
        console.log(`🚀 快速滚动阶段 ${round + 1}/3`);
        
        for (let i = 0; i < 50; i++) {
          await this.page.mouse.wheel({ deltaY: 1000 });
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      
      // 第二阶段：慢速精确滚动
      console.log('🎯 慢速精确滚动阶段...');
      for (let i = 0; i < 30; i++) {
        await this.page.mouse.wheel({ deltaY: 800 });
        await new Promise(resolve => setTimeout(resolve, 300));
      }
      
      // 第三阶段：超长等待
      console.log('⏳ 超长等待内容加载...');
      await new Promise(resolve => setTimeout(resolve, 10000));

      return true;
    } catch (error) {
      console.error('❌ 超级激进滚动失败:', error.message);
      return false;
    }
  }

  // 尝试触发更多内容加载的策略
  async triggerMoreContent() {
    try {
      console.log('🔄 尝试触发更多内容加载...');
      
      // 策略1: 模拟用户停留
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 策略2: 触发各种事件
      await this.page.evaluate(() => {
        // 触发滚动相关事件
        window.dispatchEvent(new Event('scroll'));
        window.dispatchEvent(new Event('resize'));
        window.dispatchEvent(new Event('focus'));
        
        // 尝试触发懒加载
        const events = ['lazyload', 'loadmore', 'infinite', 'scroll-end'];
        events.forEach(eventName => {
          window.dispatchEvent(new CustomEvent(eventName));
          document.dispatchEvent(new CustomEvent(eventName));
        });
        
        // 模拟用户交互
        document.body.click();
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 策略3: 键盘操作
      await this.page.keyboard.press('End');
      await new Promise(resolve => setTimeout(resolve, 2000));
      await this.page.keyboard.press('PageDown');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return true;
    } catch (error) {
      console.error('❌ 触发更多内容失败:', error.message);
      return false;
    }
  }

  // 深度滚动主流程
  async deepScrollAndCollect() {
    console.log('📜 开始深度历史帖子爬取...\n');
    
    let attempts = 0;
    let noNewPostsCount = 0;
    const maxAttempts = 100;  // 增加最大尝试次数
    const maxNoNewPosts = 15; // 增加连续无新帖子的容忍次数
    
    // 初始提取
    await this.extractPostsWithTime();
    console.log(`📊 初始帖子数: ${this.allPosts.length}`);
    
    while (attempts < maxAttempts && noNewPostsCount < maxNoNewPosts) {
      attempts++;
      console.log(`\n🔄 第${attempts}次深度滚动尝试...`);
      
      const beforeCount = this.allPosts.length;
      
      // 根据尝试次数选择不同策略
      if (attempts <= 30) {
        // 前30次：标准滚轮滚动
        await this.standardWheelScroll();
      } else if (attempts <= 60) {
        // 31-60次：超级激进滚动
        await this.performSuperAggressiveScroll();
      } else {
        // 61次以后：组合策略
        await this.performSuperAggressiveScroll();
        await this.triggerMoreContent();
      }
      
      // 提取新帖子
      const newPosts = await this.extractPostsWithTime();
      
      if (newPosts > 0) {
        noNewPostsCount = 0;
        console.log(`✅ 发现 ${newPosts} 个新帖子 (总计: ${this.allPosts.length})`);
        
        // 分析最老的帖子
        const oldestPost = this.allPosts
          .filter(p => p.postId)
          .sort((a, b) => a.postId - b.postId)[0];
        
        if (oldestPost) {
          console.log(`📅 当前最老帖子: ID ${oldestPost.postId} - ${oldestPost.title}`);
        }
        
      } else {
        noNewPostsCount++;
        console.log(`⚠️ 没有新帖子 (连续无新帖: ${noNewPostsCount})`);
      }
      
      // 每10次保存一次进度
      if (attempts % 10 === 0) {
        await this.saveProgress();
        console.log(`💾 进度已保存 (第${attempts}次尝试, ${this.allPosts.length}个帖子)`);
      }
      
      // 动态调整等待时间
      const waitTime = Math.min(2000 + attempts * 100, 8000);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
    
    console.log(`\n🎉 深度滚动完成！`);
    console.log(`📊 总共尝试 ${attempts} 次`);
    console.log(`📊 获取到 ${this.allPosts.length} 个帖子`);
    
    // 分析帖子ID范围
    const postIds = this.allPosts
      .filter(p => p.postId)
      .map(p => p.postId)
      .sort((a, b) => a - b);
    
    if (postIds.length > 0) {
      console.log(`📊 帖子ID范围: ${postIds[0]} - ${postIds[postIds.length - 1]}`);
    }
    
    return this.allPosts;
  }

  // 标准滚轮滚动
  async standardWheelScroll() {
    const viewport = await this.page.viewport();
    const centerX = viewport.width / 2;
    const centerY = viewport.height / 2;

    await this.page.mouse.move(centerX, centerY);
    
    for (let i = 0; i < 25; i++) {
      await this.page.mouse.wheel({ deltaY: 600 });
      await new Promise(resolve => setTimeout(resolve, 150));
    }

    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/deep_history_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/deep_history_posts_${timestamp}.json`;
      const txtFile = `./data/deep_history_posts_${timestamp}.txt`;
      
      // 按帖子ID排序（最新的在前）
      const sortedPosts = this.allPosts
        .filter(p => p.postId)
        .sort((a, b) => b.postId - a.postId);
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, sortedPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = sortedPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}\n   ID: ${post.postId}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  async run() {
    try {
      await this.init();
      await this.loadCookies();
      await this.checkLogin();
      
      const posts = await this.deepScrollAndCollect();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 深度爬取结果预览:');
        
        // 显示最新的10个帖子
        const recentPosts = posts
          .filter(p => p.postId)
          .sort((a, b) => b.postId - a.postId)
          .slice(0, 10);
        
        recentPosts.forEach((post, index) => {
          console.log(`${index + 1}. [ID:${post.postId}] ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
        
        // 显示最老的帖子
        const oldestPosts = posts
          .filter(p => p.postId)
          .sort((a, b) => a.postId - b.postId)
          .slice(0, 5);
        
        console.log('\n📅 最老的5个帖子:');
        oldestPosts.forEach((post, index) => {
          console.log(`${index + 1}. [ID:${post.postId}] ${post.title}`);
        });
        
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

async function main() {
  console.log('🕰️ 深度历史帖子爬虫');
  console.log('=====================================\n');
  console.log('📝 目标: 获取更多历史帖子，追溯到2020年甚至更早');
  console.log('🚀 策略: 超级激进滚动 + 多种触发机制');
  console.log('⏱️ 预计需要更长时间，请耐心等待...\n');
  
  const crawler = new DeepHistoryCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = DeepHistoryCrawler;
