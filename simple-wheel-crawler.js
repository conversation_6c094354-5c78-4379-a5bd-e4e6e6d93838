// 简单的鼠标滚轮爬虫
const puppeteer = require('puppeteer');
const fs = require('fs-extra');

class SimpleWheelCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        console.log(`✅ 找到Chrome: ${path}`);
        break;
      }
    }

    const launchOptions = {
      headless: false,
      slowMo: 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
    console.log('✅ 浏览器启动成功');
  }

  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  async checkLogin() {
    console.log('🔍 检查登录状态...');
    
    await this.page.goto('https://sns.vip.stockstar.com/sns', { 
      waitUntil: 'networkidle2' 
    });

    const isLoggedIn = await this.page.evaluate(() => {
      return !document.body.textContent.includes('登录') || 
             document.body.textContent.includes('退出');
    });

    if (!isLoggedIn) {
      console.log('🔐 需要登录，请在浏览器中手动登录...');
      console.log('登录完成后，按回车键继续...');
      
      await new Promise(resolve => {
        process.stdin.once('data', () => resolve());
      });

      const cookies = await this.page.cookies();
      await fs.ensureDir('./cookies');
      await fs.writeJson('./cookies/cookies.json', cookies);
      console.log('✅ 登录状态已保存');
    } else {
      console.log('✅ 已登录状态');
    }

    return true;
  }

  async extractPosts() {
    const posts = await this.page.evaluate(() => {
      const results = [];
      const links = document.querySelectorAll('a[href*="forumdetail"]');
      
      links.forEach(link => {
        const title = link.textContent.trim();
        const url = link.href;
        
        if (title && title.length > 3 && url) {
          results.push({ title, url });
        }
      });

      return results;
    });

    let newPostsCount = 0;
    posts.forEach(post => {
      if (!this.seenUrls.has(post.url)) {
        this.seenUrls.add(post.url);
        this.allPosts.push({
          id: this.allPosts.length + 1,
          title: post.title,
          url: post.url
        });
        newPostsCount++;
      }
    });

    return newPostsCount;
  }

  async wheelScroll() {
    try {
      // 获取页面中心点
      const viewport = await this.page.viewport();
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      // 移动鼠标到中心
      await this.page.mouse.move(centerX, centerY);
      
      // 执行滚轮滚动
      console.log('🖱️ 执行鼠标滚轮滚动...');
      
      for (let i = 0; i < 20; i++) {
        // 向下滚动
        await this.page.mouse.wheel({ deltaY: 500 });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 等待内容加载
      console.log('⏳ 等待内容加载...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      return true;
    } catch (error) {
      console.error('❌ 滚轮滚动失败:', error.message);
      return false;
    }
  }

  async scrollAndCollect() {
    console.log('📜 开始滚动收集帖子...\n');
    
    let attempts = 0;
    let noNewPostsCount = 0;
    const maxAttempts = 30;
    const maxNoNewPosts = 5;
    
    // 初始提取
    await this.extractPosts();
    console.log(`📊 初始帖子数: ${this.allPosts.length}`);
    
    while (attempts < maxAttempts && noNewPostsCount < maxNoNewPosts) {
      attempts++;
      console.log(`\n🔄 第${attempts}次滚动尝试...`);
      
      const beforeCount = this.allPosts.length;
      
      // 执行滚轮滚动
      await this.wheelScroll();
      
      // 提取新帖子
      const newPosts = await this.extractPosts();
      
      if (newPosts > 0) {
        noNewPostsCount = 0;
        console.log(`✅ 发现 ${newPosts} 个新帖子 (总计: ${this.allPosts.length})`);
      } else {
        noNewPostsCount++;
        console.log(`⚠️ 没有新帖子 (连续无新帖: ${noNewPostsCount})`);
      }
      
      // 每5次保存一次进度
      if (attempts % 5 === 0) {
        await this.saveProgress();
      }
    }
    
    console.log(`\n🎉 滚动完成！总共获取 ${this.allPosts.length} 个帖子`);
    return this.allPosts;
  }

  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/simple_wheel_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
      console.log(`💾 进度已保存 (${this.allPosts.length} 个帖子)`);
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/simple_wheel_posts_${timestamp}.json`;
      const txtFile = `./data/simple_wheel_posts_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  async run() {
    try {
      await this.init();
      await this.loadCookies();
      await this.checkLogin();
      
      const posts = await this.scrollAndCollect();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 10).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

async function main() {
  console.log('🖱️ 简单鼠标滚轮爬虫');
  console.log('=====================================\n');
  
  const crawler = new SimpleWheelCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SimpleWheelCrawler;
