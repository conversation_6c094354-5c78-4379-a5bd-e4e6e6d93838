// 获取所有帖子的专用爬虫
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class AllPostsCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,
      slowMo: 100,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });

      const isLoggedIn = await this.page.evaluate(() => {
        return !document.body.textContent.includes('登录') || 
               document.body.textContent.includes('退出');
      });

      if (!isLoggedIn) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 获取当前页面的帖子
  async getPostsFromCurrentPage() {
    try {
      // 滚动加载内容
      await this.scrollPage();

      const posts = await this.page.evaluate(() => {
        const results = [];
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        
        links.forEach(link => {
          const title = link.textContent.trim();
          const url = link.href;
          
          if (title && title.length > 3 && url) {
            results.push({ title, url });
          }
        });

        return results;
      });

      // 去重并添加到总列表
      let newPostsCount = 0;
      posts.forEach(post => {
        if (!this.seenUrls.has(post.url)) {
          this.seenUrls.add(post.url);
          this.allPosts.push({
            id: this.allPosts.length + 1,
            title: post.title,
            url: post.url
          });
          newPostsCount++;
        }
      });

      return newPostsCount;
    } catch (error) {
      console.error('❌ 获取当前页帖子失败:', error.message);
      return 0;
    }
  }

  // 滚动页面
  async scrollPage() {
    let previousHeight = 0;
    let currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
    let attempts = 0;

    while (attempts < 3 && currentHeight > previousHeight) {
      previousHeight = currentHeight;
      attempts++;

      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      await new Promise(resolve => setTimeout(resolve, 1500));
      currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
    }
  }

  // 尝试多种方法找到下一页
  async findNextPage() {
    try {
      const nextPageUrl = await this.page.evaluate(() => {
        // 方法1: 查找"下一页"相关按钮
        const buttons = document.querySelectorAll('a, button, [onclick]');
        for (const btn of buttons) {
          const text = btn.textContent.trim().toLowerCase();
          if (text.includes('下一页') || text.includes('下页') || text === '>' || text === 'next') {
            if (btn.href && btn.href !== window.location.href) {
              return btn.href;
            }
          }
        }

        // 方法2: 查找页码链接
        const currentUrl = window.location.href;
        const pageMatch = currentUrl.match(/page[=\/](\d+)/i);
        const currentPage = pageMatch ? parseInt(pageMatch[1]) : 1;
        
        const links = document.querySelectorAll('a[href]');
        for (const link of links) {
          const href = link.href;
          const linkPageMatch = href.match(/page[=\/](\d+)/i);
          
          if (linkPageMatch) {
            const pageNum = parseInt(linkPageMatch[1]);
            if (pageNum === currentPage + 1) {
              return href;
            }
          }
          
          // 检查纯数字链接
          const text = link.textContent.trim();
          if (/^\d+$/.test(text) && parseInt(text) === currentPage + 1) {
            return href;
          }
        }

        // 方法3: 构造下一页URL
        if (currentUrl.includes('page')) {
          return currentUrl.replace(/page[=\/]\d+/i, `page=${currentPage + 1}`);
        } else {
          const separator = currentUrl.includes('?') ? '&' : '?';
          return `${currentUrl}${separator}page=2`;
        }
      });

      return nextPageUrl;
    } catch (error) {
      console.error('❌ 查找下一页失败:', error.message);
      return null;
    }
  }

  // 主要爬取流程
  async crawlAllPosts() {
    try {
      console.log('📋 开始获取所有帖子...\n');
      
      let currentPage = 1;
      let consecutiveEmptyPages = 0;
      const maxEmptyPages = 3;
      const maxPages = 100; // 最大页数限制

      while (currentPage <= maxPages && consecutiveEmptyPages < maxEmptyPages) {
        console.log(`📄 正在处理第 ${currentPage} 页...`);
        
        const newPostsCount = await this.getPostsFromCurrentPage();
        
        if (newPostsCount === 0) {
          consecutiveEmptyPages++;
          console.log(`⚠️ 第 ${currentPage} 页没有新帖子 (连续空页: ${consecutiveEmptyPages})`);
        } else {
          consecutiveEmptyPages = 0;
          console.log(`✅ 第 ${currentPage} 页获取到 ${newPostsCount} 个新帖子 (总计: ${this.allPosts.length})`);
        }

        // 查找下一页
        const nextPageUrl = await this.findNextPage();
        
        if (!nextPageUrl || nextPageUrl === this.page.url()) {
          console.log('📄 没有找到下一页，爬取完成');
          break;
        }

        // 跳转到下一页
        console.log(`🔄 跳转到第 ${currentPage + 1} 页...`);
        
        try {
          await this.page.goto(nextPageUrl, { 
            waitUntil: 'networkidle2',
            timeout: 30000 
          });
          
          // 等待页面加载
          await new Promise(resolve => setTimeout(resolve, 2000));
          
        } catch (navError) {
          console.log(`❌ 跳转失败: ${navError.message}`);
          break;
        }

        currentPage++;
        
        // 每10页保存一次进度
        if (currentPage % 10 === 0) {
          await this.saveProgress();
        }
      }

      console.log(`\n🎉 爬取完成！`);
      console.log(`📊 总共获取到 ${this.allPosts.length} 个不重复的帖子`);
      console.log(`📄 遍历了 ${currentPage} 页`);

      return this.allPosts;
    } catch (error) {
      console.error('❌ 爬取过程出错:', error.message);
      return this.allPosts;
    }
  }

  // 保存进度
  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/crawl_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
      
      console.log(`💾 进度已保存 (${this.allPosts.length} 个帖子)`);
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  // 保存最终结果
  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/all_posts_${timestamp}.json`;
      const txtFile = `./data/all_posts_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    try {
      await this.init();
      await this.loadCookies();
      
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      const posts = await this.crawlAllPosts();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 5).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 5) {
          console.log(`... 还有 ${posts.length - 5} 个帖子`);
        }
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 获取所有帖子爬虫');
  console.log('=====================================\n');
  
  const crawler = new AllPostsCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = AllPostsCrawler;
