// 真正的鼠标滚轮滚动爬虫
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class WheelScrollCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,  // 显示浏览器便于观察
      slowMo: 30,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-web-security'
      ]
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      const isLoggedIn = await this.page.evaluate(() => {
        return !document.body.textContent.includes('登录') || 
               document.body.textContent.includes('退出');
      });

      if (!isLoggedIn) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 提取当前页面的所有帖子
  async extractAllPosts() {
    try {
      const posts = await this.page.evaluate(() => {
        const results = [];
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        
        links.forEach(link => {
          const title = link.textContent.trim();
          const url = link.href;
          
          if (title && title.length > 3 && url) {
            results.push({ title, url });
          }
        });

        return results;
      });

      let newPostsCount = 0;
      posts.forEach(post => {
        if (!this.seenUrls.has(post.url)) {
          this.seenUrls.add(post.url);
          this.allPosts.push({
            id: this.allPosts.length + 1,
            title: post.title,
            url: post.url
          });
          newPostsCount++;
        }
      });

      return newPostsCount;
    } catch (error) {
      console.error('❌ 提取帖子失败:', error.message);
      return 0;
    }
  }

  // 真正的鼠标滚轮滚动
  async performWheelScroll() {
    try {
      // 获取页面中心点
      const viewport = await this.page.viewport();
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      // 模拟鼠标滚轮事件
      await this.page.mouse.move(centerX, centerY);
      
      // 执行多次滚轮滚动
      for (let i = 0; i < 10; i++) {
        // 模拟鼠标滚轮向下滚动
        await this.page.mouse.wheel({ deltaY: 500 });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // 等待内容加载
      await new Promise(resolve => setTimeout(resolve, 3000));

      return true;
    } catch (error) {
      console.error('❌ 滚轮滚动失败:', error.message);
      return false;
    }
  }

  // 更激进的滚轮滚动策略
  async performAggressiveWheelScroll() {
    try {
      console.log('🖱️ 执行激进滚轮滚动...');
      
      const viewport = await this.page.viewport();
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      // 移动鼠标到页面中心
      await this.page.mouse.move(centerX, centerY);
      
      // 大量连续滚轮滚动
      for (let round = 0; round < 5; round++) {
        console.log(`🖱️ 滚轮滚动轮次 ${round + 1}/5`);
        
        for (let i = 0; i < 20; i++) {
          // 向下滚动
          await this.page.mouse.wheel({ deltaY: 800 });
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // 每轮之间等待更长时间
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // 检查是否有新内容
        const newPosts = await this.extractAllPosts();
        if (newPosts > 0) {
          console.log(`✅ 滚轮滚动轮次 ${round + 1} 发现 ${newPosts} 个新帖子`);
        }
      }

      return true;
    } catch (error) {
      console.error('❌ 激进滚轮滚动失败:', error.message);
      return false;
    }
  }

  // 组合滚动策略
  async performCombinedScroll() {
    try {
      const viewport = await this.page.viewport();
      const centerX = viewport.width / 2;
      const centerY = viewport.height / 2;

      // 策略1: 鼠标滚轮
      await this.page.mouse.move(centerX, centerY);
      for (let i = 0; i < 15; i++) {
        await this.page.mouse.wheel({ deltaY: 600 });
        await new Promise(resolve => setTimeout(resolve, 150));
      }
      
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 策略2: 键盘滚动
      await this.page.keyboard.press('End');
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 策略3: 触发滚动事件
      await this.page.evaluate(() => {
        // 触发各种可能的滚动相关事件
        window.dispatchEvent(new Event('scroll'));
        window.dispatchEvent(new Event('wheel'));
        window.dispatchEvent(new Event('mousewheel'));
        
        // 尝试触发懒加载
        const event = new CustomEvent('lazyload');
        document.dispatchEvent(event);
        
        // 滚动到底部
        window.scrollTo(0, document.body.scrollHeight);
      });
      
      await new Promise(resolve => setTimeout(resolve, 4000));

      return true;
    } catch (error) {
      console.error('❌ 组合滚动失败:', error.message);
      return false;
    }
  }

  // 主要滚动爬取流程
  async scrollToLoadAll() {
    try {
      console.log('🖱️ 开始鼠标滚轮滚动爬取所有帖子...\n');
      
      let scrollAttempts = 0;
      let consecutiveNoNewPosts = 0;
      const maxScrollAttempts = 50;
      const maxConsecutiveNoNew = 8;
      
      // 首次提取
      await this.extractAllPosts();
      console.log(`📊 初始帖子数: ${this.allPosts.length}`);
      
      while (scrollAttempts < maxScrollAttempts && consecutiveNoNewPosts < maxConsecutiveNoNew) {
        scrollAttempts++;
        
        console.log(`🖱️ 第${scrollAttempts}次滚轮滚动...`);
        
        const beforeCount = this.allPosts.length;
        
        // 根据尝试次数选择不同的滚动策略
        if (scrollAttempts <= 20) {
          // 前20次使用基础滚轮滚动
          await this.performWheelScroll();
        } else if (scrollAttempts <= 35) {
          // 21-35次使用激进滚轮滚动
          await this.performAggressiveWheelScroll();
        } else {
          // 36次以后使用组合滚动策略
          await this.performCombinedScroll();
        }
        
        // 提取新帖子
        const newPostsCount = await this.extractAllPosts();
        
        if (newPostsCount > 0) {
          consecutiveNoNewPosts = 0;
          console.log(`✅ 第${scrollAttempts}次滚动: 发现 ${newPostsCount} 个新帖子 (总计: ${this.allPosts.length})`);
        } else {
          consecutiveNoNewPosts++;
          console.log(`⚠️ 第${scrollAttempts}次滚动: 没有新帖子 (连续无新帖: ${consecutiveNoNewPosts})`);
        }
        
        // 每10次滚动保存一次进度
        if (scrollAttempts % 10 === 0) {
          await this.saveProgress();
          console.log(`💾 已保存进度 (第${scrollAttempts}次滚动, ${this.allPosts.length}个帖子)`);
        }
        
        // 随机延迟
        const delay = Math.random() * 2000 + 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      console.log(`\n🎉 滚轮滚动爬取完成！`);
      console.log(`📊 总共滚动 ${scrollAttempts} 次`);
      console.log(`📊 获取到 ${this.allPosts.length} 个不重复的帖子`);
      
      return this.allPosts;
      
    } catch (error) {
      console.error('❌ 滚轮滚动爬取失败:', error.message);
      return this.allPosts;
    }
  }

  // 保存进度
  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/wheel_scroll_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  // 保存最终结果
  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/wheel_scroll_posts_${timestamp}.json`;
      const txtFile = `./data/wheel_scroll_posts_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    try {
      await this.init();
      await this.loadCookies();
      
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      const posts = await this.scrollToLoadAll();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 10).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
        
        console.log(`\n🎯 最终统计:`);
        console.log(`📊 总帖子数: ${posts.length}`);
        console.log(`🔗 所有帖子都包含标题和链接`);
        
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🖱️ 真正的鼠标滚轮滚动爬虫');
  console.log('=====================================\n');
  console.log('📝 说明: 使用真正的鼠标滚轮事件触发无限滚动');
  console.log('🖱️ 模拟: mouse.wheel() + 多种滚动策略');
  console.log('⏱️ 预计需要较长时间，请耐心等待...\n');
  
  const crawler = new WheelScrollCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = WheelScrollCrawler;
