// 无限滚动爬虫 - 精确模拟用户滚动行为
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class InfiniteScrollCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,  // 显示浏览器便于观察
      slowMo: 30,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
    
    // 设置用户代理
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      const isLoggedIn = await this.page.evaluate(() => {
        return !document.body.textContent.includes('登录') || 
               document.body.textContent.includes('退出');
      });

      if (!isLoggedIn) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 获取当前页面的帖子数量
  async getCurrentPostCount() {
    try {
      const count = await this.page.evaluate(() => {
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        return links.length;
      });
      return count;
    } catch (error) {
      return 0;
    }
  }

  // 提取当前页面的所有帖子
  async extractAllPosts() {
    try {
      const posts = await this.page.evaluate(() => {
        const results = [];
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        
        links.forEach(link => {
          const title = link.textContent.trim();
          const url = link.href;
          
          if (title && title.length > 3 && url) {
            results.push({ title, url });
          }
        });

        return results;
      });

      // 去重并添加到总列表
      let newPostsCount = 0;
      posts.forEach(post => {
        if (!this.seenUrls.has(post.url)) {
          this.seenUrls.add(post.url);
          this.allPosts.push({
            id: this.allPosts.length + 1,
            title: post.title,
            url: post.url
          });
          newPostsCount++;
        }
      });

      return newPostsCount;
    } catch (error) {
      console.error('❌ 提取帖子失败:', error.message);
      return 0;
    }
  }

  // 精确的滚动策略 - 模拟真实用户行为
  async performRealUserScroll() {
    try {
      // 获取当前页面高度
      const beforeHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      // 策略1: 模拟鼠标滚轮滚动
      await this.page.evaluate(() => {
        // 模拟多次小幅度滚动
        let scrolled = 0;
        const scrollStep = 300;
        const maxScroll = document.body.scrollHeight;
        
        const scrollInterval = setInterval(() => {
          window.scrollBy(0, scrollStep);
          scrolled += scrollStep;
          
          if (scrolled >= maxScroll) {
            clearInterval(scrollInterval);
          }
        }, 150); // 每150ms滚动一次
        
        // 等待滚动完成
        return new Promise(resolve => {
          setTimeout(() => {
            clearInterval(scrollInterval);
            resolve();
          }, 3000);
        });
      });
      
      // 等待内容加载
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 策略2: 滚动到绝对底部
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      
      // 等待更长时间让内容加载
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 策略3: 触发可能的加载事件
      await this.page.evaluate(() => {
        // 触发scroll事件
        window.dispatchEvent(new Event('scroll'));
        
        // 触发resize事件
        window.dispatchEvent(new Event('resize'));
        
        // 模拟到达底部
        const event = new CustomEvent('reachBottom');
        window.dispatchEvent(event);
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 检查页面高度是否变化
      const afterHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      console.log(`📏 滚动前高度: ${beforeHeight}px, 滚动后高度: ${afterHeight}px`);
      
      return afterHeight > beforeHeight;
      
    } catch (error) {
      console.error('❌ 滚动失败:', error.message);
      return false;
    }
  }

  // 等待新内容加载
  async waitForNewContent(previousCount, timeout = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const currentCount = await this.getCurrentPostCount();
      if (currentCount > previousCount) {
        console.log(`✅ 检测到新内容: ${currentCount - previousCount} 个新帖子`);
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return false;
  }

  // 主要滚动爬取流程
  async scrollToLoadAll() {
    try {
      console.log('📜 开始无限滚动爬取所有帖子...\n');
      
      let scrollAttempts = 0;
      let consecutiveNoNewPosts = 0;
      const maxScrollAttempts = 100;  // 增加最大滚动次数
      const maxConsecutiveNoNew = 15; // 增加连续无新帖子的容忍次数
      
      // 首次提取
      await this.extractAllPosts();
      console.log(`📊 初始帖子数: ${this.allPosts.length}`);
      
      while (scrollAttempts < maxScrollAttempts && consecutiveNoNewPosts < maxConsecutiveNoNew) {
        scrollAttempts++;
        
        console.log(`📜 第${scrollAttempts}次滚动...`);
        
        // 获取滚动前的帖子数量
        const beforeCount = this.allPosts.length;
        const beforeDOMCount = await this.getCurrentPostCount();
        
        // 执行真实用户滚动
        const heightChanged = await this.performRealUserScroll();
        
        // 等待新内容加载
        const hasNewContent = await this.waitForNewContent(beforeDOMCount, 8000);
        
        // 提取新帖子
        const newPostsCount = await this.extractAllPosts();
        
        if (newPostsCount > 0) {
          consecutiveNoNewPosts = 0;
          console.log(`✅ 第${scrollAttempts}次滚动: 发现 ${newPostsCount} 个新帖子 (总计: ${this.allPosts.length})`);
        } else {
          consecutiveNoNewPosts++;
          console.log(`⚠️ 第${scrollAttempts}次滚动: 没有新帖子 (连续无新帖: ${consecutiveNoNewPosts})`);
        }
        
        // 如果页面高度没有变化且没有新内容，可能真的到底了
        if (!heightChanged && !hasNewContent && newPostsCount === 0) {
          console.log('📄 页面高度和内容都没有变化，可能已经到底了');
          
          // 最后的尝试 - 更激进的滚动
          console.log('🔄 执行最后的激进滚动尝试...');
          for (let i = 0; i < 5; i++) {
            await this.page.evaluate(() => {
              // 快速滚动到底部
              window.scrollTo(0, document.body.scrollHeight);
              
              // 稍微往上滚再往下滚
              setTimeout(() => {
                window.scrollBy(0, -500);
                setTimeout(() => {
                  window.scrollTo(0, document.body.scrollHeight);
                }, 200);
              }, 500);
            });
            
            await new Promise(resolve => setTimeout(resolve, 4000));
            
            const finalNewPosts = await this.extractAllPosts();
            if (finalNewPosts > 0) {
              console.log(`🎯 激进滚动发现 ${finalNewPosts} 个新帖子`);
              consecutiveNoNewPosts = 0;
              break;
            }
          }
        }
        
        // 每10次滚动保存一次进度
        if (scrollAttempts % 10 === 0) {
          await this.saveProgress();
          console.log(`💾 已保存进度 (第${scrollAttempts}次滚动, ${this.allPosts.length}个帖子)`);
        }
        
        // 随机延迟，模拟真实用户行为
        const delay = Math.random() * 2000 + 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      console.log(`\n🎉 滚动爬取完成！`);
      console.log(`📊 总共滚动 ${scrollAttempts} 次`);
      console.log(`📊 获取到 ${this.allPosts.length} 个不重复的帖子`);
      
      return this.allPosts;
      
    } catch (error) {
      console.error('❌ 滚动爬取失败:', error.message);
      return this.allPosts;
    }
  }

  // 保存进度
  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/infinite_scroll_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  // 保存最终结果
  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/infinite_scroll_posts_${timestamp}.json`;
      const txtFile = `./data/infinite_scroll_posts_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    try {
      await this.init();
      await this.loadCookies();
      
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      const posts = await this.scrollToLoadAll();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 10).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
        
        console.log(`\n🎯 最终统计:`);
        console.log(`📊 总帖子数: ${posts.length}`);
        console.log(`🔗 所有帖子都包含标题和链接`);
        
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 无限滚动帖子爬虫 (精确版)');
  console.log('=====================================\n');
  console.log('📝 说明: 精确模拟用户滚动行为，获取所有帖子');
  console.log('🖱️ 模拟鼠标滚轮、等待加载、检测新内容');
  console.log('⏱️ 预计需要较长时间，请耐心等待...\n');
  
  const crawler = new InfiniteScrollCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = InfiniteScrollCrawler;
