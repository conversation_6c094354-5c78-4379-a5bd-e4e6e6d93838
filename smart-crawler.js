// 智能爬虫 - 尝试多种URL和滚动策略获取所有帖子
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class SmartCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,
      slowMo: 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });

      const isLoggedIn = await this.page.evaluate(() => {
        return !document.body.textContent.includes('登录') || 
               document.body.textContent.includes('退出');
      });

      if (!isLoggedIn) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 提取当前页面的帖子
  async extractPosts() {
    try {
      const posts = await this.page.evaluate(() => {
        const results = [];
        
        // 尝试多种选择器
        const selectors = [
          'a[href*="forumdetail"]',
          'a[href*="detail"]',
          'a[href*="post"]',
          'a[href*="topic"]'
        ];
        
        for (const selector of selectors) {
          const links = document.querySelectorAll(selector);
          
          links.forEach(link => {
            const title = link.textContent.trim();
            const url = link.href;
            
            if (title && title.length > 3 && url && url.includes('forumdetail')) {
              results.push({ title, url });
            }
          });
          
          if (results.length > 0) break;
        }

        return results;
      });

      let newPostsCount = 0;
      posts.forEach(post => {
        if (!this.seenUrls.has(post.url)) {
          this.seenUrls.add(post.url);
          this.allPosts.push({
            id: this.allPosts.length + 1,
            title: post.title,
            url: post.url
          });
          newPostsCount++;
        }
      });

      return newPostsCount;
    } catch (error) {
      console.error('❌ 提取帖子失败:', error.message);
      return 0;
    }
  }

  // 强化滚动策略
  async performAdvancedScroll() {
    try {
      // 获取初始高度
      const initialHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      // 策略1: 缓慢滚动
      await this.page.evaluate(() => {
        return new Promise((resolve) => {
          let totalHeight = 0;
          const distance = 100;
          const timer = setInterval(() => {
            const scrollHeight = document.body.scrollHeight;
            window.scrollBy(0, distance);
            totalHeight += distance;

            if(totalHeight >= scrollHeight){
              clearInterval(timer);
              resolve();
            }
          }, 100);
        });
      });
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 策略2: 滚动到底部并等待
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });
      
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 策略3: 模拟用户行为
      await this.page.evaluate(() => {
        // 滚动到底部
        window.scrollTo(0, document.body.scrollHeight);
        
        // 稍微往上滚
        setTimeout(() => {
          window.scrollBy(0, -200);
          
          // 再滚到底部
          setTimeout(() => {
            window.scrollTo(0, document.body.scrollHeight);
          }, 500);
        }, 1000);
      });
      
      await new Promise(resolve => setTimeout(resolve, 4000));
      
      // 检查高度是否变化
      const finalHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      return finalHeight > initialHeight;
      
    } catch (error) {
      console.error('❌ 滚动失败:', error.message);
      return false;
    }
  }

  // 尝试不同的URL
  async tryDifferentUrls() {
    const urls = [
      'https://sns.vip.stockstar.com/sns',
      'https://sns.vip.stockstar.com/sns?page=1',
      'https://sns.vip.stockstar.com/sns/forum',
      'https://sns.vip.stockstar.com/sns/list',
      'https://sns.vip.stockstar.com/sns/index'
    ];

    for (const url of urls) {
      try {
        console.log(`🔍 尝试URL: ${url}`);
        
        await this.page.goto(url, { 
          waitUntil: 'networkidle2',
          timeout: 30000 
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const newPosts = await this.extractPosts();
        if (newPosts > 0) {
          console.log(`✅ 在 ${url} 发现 ${newPosts} 个新帖子`);
          
          // 尝试滚动加载更多
          await this.performAdvancedScroll();
          await this.extractPosts();
        } else {
          console.log(`⚠️ ${url} 没有发现新帖子`);
        }
        
      } catch (error) {
        console.log(`❌ 访问 ${url} 失败: ${error.message}`);
      }
    }
  }

  // 主要爬取流程
  async crawlAllPosts() {
    try {
      console.log('📋 开始智能爬取所有帖子...\n');
      
      // 1. 尝试不同的URL
      await this.tryDifferentUrls();
      
      // 2. 回到主页面进行深度滚动
      console.log('\n🔄 回到主页面进行深度滚动...');
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });
      
      // 3. 执行多轮深度滚动
      let scrollRounds = 0;
      let consecutiveNoNew = 0;
      const maxScrollRounds = 50;
      const maxConsecutiveNoNew = 10;
      
      while (scrollRounds < maxScrollRounds && consecutiveNoNew < maxConsecutiveNoNew) {
        scrollRounds++;
        console.log(`📜 第${scrollRounds}轮深度滚动...`);
        
        const beforeCount = this.allPosts.length;
        
        // 执行强化滚动
        const heightChanged = await this.performAdvancedScroll();
        
        // 提取帖子
        const newPosts = await this.extractPosts();
        
        if (newPosts > 0) {
          consecutiveNoNew = 0;
          console.log(`✅ 第${scrollRounds}轮发现 ${newPosts} 个新帖子 (总计: ${this.allPosts.length})`);
        } else {
          consecutiveNoNew++;
          console.log(`⚠️ 第${scrollRounds}轮没有新帖子 (连续无新帖: ${consecutiveNoNew})`);
        }
        
        // 如果页面高度没有变化且没有新帖子，可能真的到底了
        if (!heightChanged && newPosts === 0) {
          console.log('📄 页面高度和内容都没有变化，可能已经到底了');
          
          // 最后尝试几次
          for (let i = 0; i < 3; i++) {
            await this.performAdvancedScroll();
            const finalNew = await this.extractPosts();
            if (finalNew > 0) {
              console.log(`🎯 最终尝试发现 ${finalNew} 个新帖子`);
              consecutiveNoNew = 0;
              break;
            }
          }
        }
        
        // 每10轮保存一次进度
        if (scrollRounds % 10 === 0) {
          await this.saveProgress();
        }
      }
      
      console.log(`\n🎉 智能爬取完成！`);
      console.log(`📊 总共执行 ${scrollRounds} 轮滚动`);
      console.log(`📊 获取到 ${this.allPosts.length} 个不重复的帖子`);
      
      return this.allPosts;
      
    } catch (error) {
      console.error('❌ 智能爬取失败:', error.message);
      return this.allPosts;
    }
  }

  // 保存进度
  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/smart_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  // 保存最终结果
  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/all_posts_smart_${timestamp}.json`;
      const txtFile = `./data/all_posts_smart_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    try {
      await this.init();
      await this.loadCookies();
      
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      const posts = await this.crawlAllPosts();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 10).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
        
        console.log(`\n🎯 最终统计:`);
        console.log(`📊 总帖子数: ${posts.length}`);
        console.log(`🔗 所有帖子都包含标题和链接`);
        
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 智能全站帖子爬虫');
  console.log('=====================================\n');
  console.log('📝 说明: 这个爬虫会尝试多种策略获取所有帖子');
  console.log('🔍 包括: 不同URL、深度滚动、多种滚动策略');
  console.log('⏱️ 预计需要较长时间，请耐心等待...\n');
  
  const crawler = new SmartCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = SmartCrawler;
