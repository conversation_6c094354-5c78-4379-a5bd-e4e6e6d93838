// 简化版爬虫 - 只获取帖子标题和链接
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class SimpleCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    // Chrome路径
    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,  // 显示浏览器便于调试
      slowMo: 100,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 简单登录（如果需要）
  async loginIfNeeded() {
    try {
      // 先尝试直接访问目标页面
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });

      // 检查是否需要登录
      const needLogin = await this.page.evaluate(() => {
        return document.body.textContent.includes('登录') && 
               !document.body.textContent.includes('退出');
      });

      if (needLogin) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        // 等待用户手动登录
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        // 保存cookies
        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 获取帖子列表
  async getPostList() {
    try {
      console.log('📋 开始获取帖子列表...');

      // 滚动加载更多内容
      await this.scrollToLoadMore();

      // 提取帖子标题和链接
      const posts = await this.page.evaluate(() => {
        const results = [];
        
        // 查找所有包含链接的元素
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        
        links.forEach((link, index) => {
          const title = link.textContent.trim();
          const url = link.href;
          
          // 过滤掉空标题和重复链接
          if (title && title.length > 3 && url) {
            results.push({
              id: index + 1,
              title: title,
              url: url
            });
          }
        });

        // 去重（基于URL）
        const uniqueResults = [];
        const seenUrls = new Set();
        
        results.forEach(post => {
          if (!seenUrls.has(post.url)) {
            seenUrls.add(post.url);
            uniqueResults.push(post);
          }
        });

        return uniqueResults;
      });

      console.log(`✅ 获取到 ${posts.length} 个帖子`);
      return posts;

    } catch (error) {
      console.error('❌ 获取帖子列表失败:', error.message);
      return [];
    }
  }

  // 滚动加载更多内容
  async scrollToLoadMore() {
    let previousHeight = 0;
    let currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
    let scrollAttempts = 0;
    const maxScrollAttempts = 5; // 最多滚动5次

    console.log(`📏 开始滚动加载，初始高度: ${currentHeight}px`);

    while (scrollAttempts < maxScrollAttempts && currentHeight > previousHeight) {
      previousHeight = currentHeight;
      scrollAttempts++;

      console.log(`📜 第${scrollAttempts}次滚动...`);

      // 滚动到页面底部
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      // 等待内容加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查新高度
      currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      if (currentHeight === previousHeight) {
        console.log('📄 页面高度未变化，停止滚动');
        break;
      }
    }

    console.log(`✅ 滚动完成，最终高度: ${currentHeight}px`);
  }

  // 保存数据
  async saveData(posts) {
    try {
      await fs.ensureDir('./data');
      
      // 保存简化的数据
      const outputFile = './data/post_list.json';
      await fs.writeJson(outputFile, posts, { spaces: 2 });
      
      console.log(`💾 数据已保存到: ${outputFile}`);
      console.log(`📊 共保存 ${posts.length} 个帖子`);

      // 生成简单的文本列表
      const textList = posts.map((post, index) => 
        `${index + 1}. ${post.title}\n   链接: ${post.url}`
      ).join('\n\n');

      const textFile = './data/post_list.txt';
      await fs.writeFile(textFile, textList, 'utf8');
      console.log(`📝 文本列表已保存到: ${textFile}`);

      return outputFile;
    } catch (error) {
      console.error('❌ 保存数据失败:', error.message);
    }
  }

  // 主要执行流程
  async run() {
    try {
      await this.init();
      
      // 加载cookies
      await this.loadCookies();
      
      // 登录检查
      const loginSuccess = await this.loginIfNeeded();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      // 获取帖子列表
      const posts = await this.getPostList();
      
      if (posts.length > 0) {
        // 保存数据
        await this.saveData(posts);
        
        // 显示前几个帖子作为示例
        console.log('\n📋 帖子列表预览:');
        posts.slice(0, 5).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
          console.log(`   ${post.url}\n`);
        });
        
        if (posts.length > 5) {
          console.log(`... 还有 ${posts.length - 5} 个帖子，详见输出文件`);
        }
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 简化版帖子列表爬虫');
  console.log('=====================================\n');
  
  const crawler = new SimpleCrawler();
  await crawler.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = SimpleCrawler;
