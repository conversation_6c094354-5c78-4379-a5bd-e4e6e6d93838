// 简化版爬虫 - 只获取帖子标题和链接
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class SimpleCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    // Chrome路径
    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,  // 显示浏览器便于调试
      slowMo: 100,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 简单登录（如果需要）
  async loginIfNeeded() {
    try {
      // 先尝试直接访问目标页面
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });

      // 检查是否需要登录
      const needLogin = await this.page.evaluate(() => {
        return document.body.textContent.includes('登录') && 
               !document.body.textContent.includes('退出');
      });

      if (needLogin) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        // 等待用户手动登录
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        // 保存cookies
        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 获取单页帖子列表
  async getPostsFromPage() {
    try {
      // 滚动加载更多内容
      await this.scrollToLoadMore();

      // 提取帖子标题和链接
      const posts = await this.page.evaluate(() => {
        const results = [];

        // 查找所有包含链接的元素
        const links = document.querySelectorAll('a[href*="forumdetail"]');

        links.forEach((link, index) => {
          const title = link.textContent.trim();
          const url = link.href;

          // 过滤掉空标题和重复链接
          if (title && title.length > 3 && url) {
            results.push({
              title: title,
              url: url
            });
          }
        });

        // 去重（基于URL）
        const uniqueResults = [];
        const seenUrls = new Set();

        results.forEach(post => {
          if (!seenUrls.has(post.url)) {
            seenUrls.add(post.url);
            uniqueResults.push(post);
          }
        });

        return uniqueResults;
      });

      return posts;

    } catch (error) {
      console.error('❌ 获取当前页帖子失败:', error.message);
      return [];
    }
  }

  // 检查是否有下一页并获取下一页URL
  async getNextPageUrl() {
    try {
      const nextPageInfo = await this.page.evaluate(() => {
        // 方法1: 查找"下一页"按钮
        const nextButtons = [
          ...document.querySelectorAll('a'),
          ...document.querySelectorAll('button'),
          ...document.querySelectorAll('[onclick]')
        ];

        for (const btn of nextButtons) {
          const text = btn.textContent.trim();
          if (text.includes('下一页') || text.includes('下页') || text === '>') {
            if (btn.href) {
              return { url: btn.href, method: '下一页按钮' };
            }
          }
        }

        // 方法2: 查找页码链接
        const pageLinks = document.querySelectorAll('a[href]');
        const currentUrl = window.location.href;
        let maxPageNum = 0;
        let nextPageUrl = null;

        // 提取当前页码
        const currentPageMatch = currentUrl.match(/page[=\/](\d+)/i);
        const currentPage = currentPageMatch ? parseInt(currentPageMatch[1]) : 1;

        for (const link of pageLinks) {
          const href = link.href;
          const text = link.textContent.trim();

          // 检查是否是页码链接
          const pageMatch = href.match(/page[=\/](\d+)/i);
          if (pageMatch) {
            const pageNum = parseInt(pageMatch[1]);
            if (pageNum === currentPage + 1) {
              return { url: href, method: '页码链接' };
            }
            if (pageNum > maxPageNum) {
              maxPageNum = pageNum;
              nextPageUrl = href;
            }
          }

          // 检查纯数字链接
          if (/^\d+$/.test(text)) {
            const pageNum = parseInt(text);
            if (pageNum === currentPage + 1) {
              return { url: href, method: '数字链接' };
            }
          }
        }

        // 方法3: 尝试构造下一页URL
        if (currentUrl.includes('page')) {
          const nextPage = currentPage + 1;
          const nextUrl = currentUrl.replace(/page[=\/]\d+/i, `page=${nextPage}`);
          if (nextUrl !== currentUrl) {
            return { url: nextUrl, method: '构造URL' };
          }
        } else {
          // 如果当前URL没有page参数，尝试添加page=2
          const separator = currentUrl.includes('?') ? '&' : '?';
          const nextUrl = `${currentUrl}${separator}page=2`;
          return { url: nextUrl, method: '添加page参数' };
        }

        return null;
      });

      if (nextPageInfo) {
        console.log(`🔍 找到下一页 (${nextPageInfo.method}): ${nextPageInfo.url}`);
        return nextPageInfo.url;
      }

      return null;
    } catch (error) {
      console.error('❌ 检查下一页失败:', error.message);
      return null;
    }
  }

  // 获取所有帖子列表
  async getAllPosts() {
    try {
      console.log('📋 开始获取所有帖子列表...');

      let allPosts = [];
      let currentPage = 1;
      let maxPages = 50; // 设置最大页数限制，避免无限循环

      while (currentPage <= maxPages) {
        console.log(`📄 正在获取第 ${currentPage} 页...`);

        // 获取当前页的帖子
        const pagePosts = await this.getPostsFromPage();

        if (pagePosts.length === 0) {
          console.log('📄 当前页没有帖子，可能已到最后一页');
          break;
        }

        console.log(`✅ 第 ${currentPage} 页获取到 ${pagePosts.length} 个帖子`);
        allPosts = allPosts.concat(pagePosts);

        // 检查是否有下一页
        const nextPageUrl = await this.getNextPageUrl();

        if (!nextPageUrl) {
          console.log('📄 没有更多页面了');
          break;
        }

        // 访问下一页
        console.log(`🔄 跳转到下一页: ${nextPageUrl}`);
        await this.page.goto(nextPageUrl, { waitUntil: 'networkidle2' });

        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        currentPage++;
      }

      // 最终去重
      const uniquePosts = [];
      const seenUrls = new Set();

      allPosts.forEach((post, index) => {
        if (!seenUrls.has(post.url)) {
          seenUrls.add(post.url);
          uniquePosts.push({
            id: uniquePosts.length + 1,
            title: post.title,
            url: post.url
          });
        }
      });

      console.log(`🎉 总共获取到 ${uniquePosts.length} 个不重复的帖子（遍历了 ${currentPage - 1} 页）`);
      return uniquePosts;

    } catch (error) {
      console.error('❌ 获取所有帖子失败:', error.message);
      return [];
    }
  }

  // 滚动加载更多内容
  async scrollToLoadMore() {
    let previousHeight = 0;
    let currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
    let scrollAttempts = 0;
    const maxScrollAttempts = 5; // 最多滚动5次

    console.log(`📏 开始滚动加载，初始高度: ${currentHeight}px`);

    while (scrollAttempts < maxScrollAttempts && currentHeight > previousHeight) {
      previousHeight = currentHeight;
      scrollAttempts++;

      console.log(`📜 第${scrollAttempts}次滚动...`);

      // 滚动到页面底部
      await this.page.evaluate(() => {
        window.scrollTo(0, document.body.scrollHeight);
      });

      // 等待内容加载
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查新高度
      currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
      
      if (currentHeight === previousHeight) {
        console.log('📄 页面高度未变化，停止滚动');
        break;
      }
    }

    console.log(`✅ 滚动完成，最终高度: ${currentHeight}px`);
  }

  // 保存数据
  async saveData(posts) {
    try {
      await fs.ensureDir('./data');
      
      // 保存简化的数据
      const outputFile = './data/post_list.json';
      await fs.writeJson(outputFile, posts, { spaces: 2 });
      
      console.log(`💾 数据已保存到: ${outputFile}`);
      console.log(`📊 共保存 ${posts.length} 个帖子`);

      // 生成简单的文本列表
      const textList = posts.map((post, index) => 
        `${index + 1}. ${post.title}\n   链接: ${post.url}`
      ).join('\n\n');

      const textFile = './data/post_list.txt';
      await fs.writeFile(textFile, textList, 'utf8');
      console.log(`📝 文本列表已保存到: ${textFile}`);

      return outputFile;
    } catch (error) {
      console.error('❌ 保存数据失败:', error.message);
    }
  }

  // 主要执行流程
  async run() {
    try {
      await this.init();
      
      // 加载cookies
      await this.loadCookies();
      
      // 登录检查
      const loginSuccess = await this.loginIfNeeded();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      // 获取所有帖子列表
      const posts = await this.getAllPosts();
      
      if (posts.length > 0) {
        // 保存数据
        await this.saveData(posts);
        
        // 显示前几个帖子作为示例
        console.log('\n📋 帖子列表预览:');
        posts.slice(0, 5).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
          console.log(`   ${post.url}\n`);
        });
        
        if (posts.length > 5) {
          console.log(`... 还有 ${posts.length - 5} 个帖子，详见输出文件`);
        }
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 简化版帖子列表爬虫');
  console.log('=====================================\n');
  
  const crawler = new SimpleCrawler();
  await crawler.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = SimpleCrawler;
