# 证券之星论坛爬虫 - 简化版

## 🎯 功能说明

这是一个极简版的证券之星论坛爬虫，**只获取帖子标题和链接地址**，不获取其他任何信息。

## ✅ 已验证功能

- ✅ 获取帖子标题
- ✅ 获取帖子链接地址
- ✅ 自动去重
- ✅ 保存为JSON和文本格式
- ✅ 生成Markdown列表

## 🚀 使用方法

### 1. 运行爬虫
```bash
node simple-crawler.js
```

### 2. 转换为Markdown
```bash
node simple-markdown.js
```

## 📁 输出文件

### 数据文件
- `data/post_list.json` - JSON格式的帖子列表
- `data/post_list.txt` - 文本格式的帖子列表

### Markdown文件
- `simple-output/帖子列表.md` - 简单列表格式
- `simple-output/帖子列表_表格.md` - 表格格式

## 📊 输出示例

### JSON格式 (`data/post_list.json`)
```json
[
  {
    "id": 1,
    "title": "关于迈瑞医疗划线提问的提问",
    "url": "https://sns.vip.stockstar.com/sns/forumdetail/6613"
  },
  {
    "id": 2,
    "title": "太疯狂了！巨头们继续卷不停",
    "url": "https://sns.vip.stockstar.com/sns/forumdetail/6615"
  }
]
```

### Markdown列表格式
```markdown
# 证券之星论坛帖子列表

1. [关于迈瑞医疗划线提问的提问](https://sns.vip.stockstar.com/sns/forumdetail/6613)
2. [太疯狂了！巨头们继续卷不停](https://sns.vip.stockstar.com/sns/forumdetail/6615)
```

### Markdown表格格式
```markdown
| 序号 | 帖子标题 | 链接 |
|------|----------|------|
| 1 | 关于迈瑞医疗划线提问的提问 | [查看原文](链接) |
| 2 | 太疯狂了！巨头们继续卷不停 | [查看原文](链接) |
```

## 🔧 技术特点

### 极简设计
- **只获取必要信息**：标题 + 链接
- **代码简洁**：核心功能不到200行
- **运行快速**：无需处理复杂内容

### 自动化程度高
- **自动登录检测**：使用已保存的cookies
- **自动去重**：基于URL去除重复帖子
- **多格式输出**：JSON、文本、Markdown

### 用户友好
- **手动登录**：首次使用时在浏览器中手动登录
- **状态保存**：登录状态自动保存
- **清晰输出**：多种格式便于使用

## 📝 使用流程

### 首次使用
1. 运行 `node simple-crawler.js`
2. 在弹出的浏览器中手动登录
3. 登录完成后按回车键继续
4. 程序自动获取帖子列表并保存

### 后续使用
1. 直接运行 `node simple-crawler.js`
2. 程序使用保存的登录状态自动运行
3. 运行 `node simple-markdown.js` 生成Markdown文件

## 🎉 测试结果

最近测试（2025-07-14）：
- ✅ 成功获取 10 个帖子
- ✅ 生成完整的JSON数据
- ✅ 生成文本列表
- ✅ 生成两种Markdown格式

## 📋 获取的数据示例

```
1. 关于迈瑞医疗划线提问的提问
   https://sns.vip.stockstar.com/sns/forumdetail/6613

2. 太疯狂了！巨头们继续卷不停
   https://sns.vip.stockstar.com/sns/forumdetail/6615

3. 中国神华业绩预告同比双位数下跌
   https://sns.vip.stockstar.com/sns/forumdetail/6614
```

## ⚙️ 配置说明

程序使用 `src/config.js` 中的基本配置，但简化版主要依赖：
- 自动登录检测
- Cookie管理
- 基本的反爬虫延迟

## 🔍 故障排除

### 常见问题
1. **首次运行需要手动登录**：这是正常的，登录一次后会保存状态
2. **获取数据为空**：检查网络连接和登录状态
3. **浏览器启动失败**：确保已安装Chrome浏览器

### 调试方法
- 程序会显示详细的运行日志
- 浏览器窗口可见，便于观察运行过程
- 检查生成的数据文件确认结果

## 🎯 适用场景

这个简化版特别适合：
- ✅ 只需要帖子标题和链接的场景
- ✅ 快速获取论坛最新帖子列表
- ✅ 生成帖子索引或目录
- ✅ 作为其他系统的数据源

## 📈 优势

相比完整版：
- ✅ **更简单**：只关注核心需求
- ✅ **更快速**：无需处理复杂内容
- ✅ **更稳定**：减少了出错的可能性
- ✅ **更易用**：输出格式清晰明了

这个简化版完美满足了您"只需要获取所有帖子的清单列表、对应链接地址"的需求！
