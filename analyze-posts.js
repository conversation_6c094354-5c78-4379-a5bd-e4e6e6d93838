// 分析已获取帖子的工具
const fs = require('fs-extra');

class PostAnalyzer {
  constructor() {
    this.posts = [];
  }

  async loadPosts() {
    try {
      // 查找最新的帖子数据文件
      const dataFiles = [
        './data/simple_wheel_posts_2025-07-14T10-49-36-623Z.json',
        './data/deep_history_progress.json'
      ];

      for (const file of dataFiles) {
        if (await fs.pathExists(file)) {
          console.log(`📖 加载数据文件: ${file}`);
          const data = await fs.readJson(file);
          
          if (data.posts) {
            this.posts = data.posts;
          } else {
            this.posts = data;
          }
          
          console.log(`✅ 加载了 ${this.posts.length} 个帖子`);
          break;
        }
      }

      if (this.posts.length === 0) {
        console.log('❌ 未找到帖子数据');
        return false;
      }

      return true;
    } catch (error) {
      console.error('❌ 加载帖子数据失败:', error.message);
      return false;
    }
  }

  analyzePostIds() {
    console.log('\n📊 帖子ID分析:');
    console.log('=====================================');

    // 提取所有帖子ID
    const postIds = this.posts
      .map(post => {
        const match = post.url.match(/forumdetail\/(\d+)/);
        return match ? parseInt(match[1]) : null;
      })
      .filter(id => id !== null)
      .sort((a, b) => a - b);

    if (postIds.length === 0) {
      console.log('❌ 没有找到有效的帖子ID');
      return;
    }

    console.log(`📊 总帖子数: ${postIds.length}`);
    console.log(`📊 最小ID: ${postIds[0]}`);
    console.log(`📊 最大ID: ${postIds[postIds.length - 1]}`);
    console.log(`📊 ID范围: ${postIds[postIds.length - 1] - postIds[0]}`);

    // 分析ID分布
    const ranges = [
      { name: '6000+', min: 6000, max: Infinity },
      { name: '5000-5999', min: 5000, max: 5999 },
      { name: '4000-4999', min: 4000, max: 4999 },
      { name: '3000-3999', min: 3000, max: 3999 },
      { name: '2000-2999', min: 2000, max: 2999 },
      { name: '1000-1999', min: 1000, max: 1999 },
      { name: '0-999', min: 0, max: 999 }
    ];

    console.log('\n📈 ID分布统计:');
    ranges.forEach(range => {
      const count = postIds.filter(id => id >= range.min && id <= range.max).length;
      if (count > 0) {
        console.log(`  ${range.name}: ${count} 个帖子`);
      }
    });

    // 显示最老的10个帖子
    console.log('\n📅 最老的10个帖子:');
    const oldestPosts = this.posts
      .map(post => {
        const match = post.url.match(/forumdetail\/(\d+)/);
        const postId = match ? parseInt(match[1]) : null;
        return { ...post, postId };
      })
      .filter(post => post.postId)
      .sort((a, b) => a.postId - b.postId)
      .slice(0, 10);

    oldestPosts.forEach((post, index) => {
      console.log(`${index + 1}. [ID:${post.postId}] ${post.title}`);
    });

    // 显示最新的10个帖子
    console.log('\n📅 最新的10个帖子:');
    const newestPosts = this.posts
      .map(post => {
        const match = post.url.match(/forumdetail\/(\d+)/);
        const postId = match ? parseInt(match[1]) : null;
        return { ...post, postId };
      })
      .filter(post => post.postId)
      .sort((a, b) => b.postId - a.postId)
      .slice(0, 10);

    newestPosts.forEach((post, index) => {
      console.log(`${index + 1}. [ID:${post.postId}] ${post.title}`);
    });
  }

  estimateTimeRange() {
    console.log('\n🕰️ 时间范围估算:');
    console.log('=====================================');

    const postIds = this.posts
      .map(post => {
        const match = post.url.match(/forumdetail\/(\d+)/);
        return match ? parseInt(match[1]) : null;
      })
      .filter(id => id !== null);

    if (postIds.length === 0) return;

    const minId = Math.min(...postIds);
    const maxId = Math.max(...postIds);

    console.log(`📊 ID范围: ${minId} - ${maxId}`);
    
    // 基于ID推测时间（这是估算）
    if (maxId > 6000) {
      console.log('📅 最新帖子: 可能是2025年7月');
    }
    
    if (minId > 3000) {
      console.log('📅 最老帖子: 可能是2025年3月或更晚');
      console.log('⚠️ 说明: 还有大量2020-2024年的历史帖子未获取');
    } else if (minId > 1000) {
      console.log('📅 最老帖子: 可能是2023-2024年');
      console.log('⚠️ 说明: 还有2020-2022年的历史帖子未获取');
    } else {
      console.log('📅 最老帖子: 可能追溯到2020年或更早');
      console.log('✅ 说明: 已获取到较早的历史帖子');
    }
  }

  generateReport() {
    console.log('\n📋 获取建议:');
    console.log('=====================================');

    const postIds = this.posts
      .map(post => {
        const match = post.url.match(/forumdetail\/(\d+)/);
        return match ? parseInt(match[1]) : null;
      })
      .filter(id => id !== null);

    const minId = Math.min(...postIds);

    if (minId > 3000) {
      console.log('🎯 建议: 需要更深度的滚动来获取历史帖子');
      console.log('💡 策略:');
      console.log('   1. 增加滚动次数和等待时间');
      console.log('   2. 尝试更激进的滚动策略');
      console.log('   3. 可能需要滚动数百次才能到达2020年的帖子');
      console.log('   4. 考虑分批次运行，每次运行更长时间');
    } else if (minId > 1000) {
      console.log('🎯 建议: 继续深度滚动可能获取更多历史帖子');
      console.log('💡 策略: 增加滚动轮次，延长等待时间');
    } else {
      console.log('🎯 建议: 已获取到相当早期的帖子');
      console.log('💡 策略: 可以尝试继续滚动获取更多');
    }

    console.log('\n⏱️ 预估:');
    console.log(`   当前最老帖子ID: ${minId}`);
    console.log(`   如果要到达ID 1000: 可能需要滚动 ${Math.ceil((minId - 1000) / 20)} 轮`);
    console.log(`   如果要到达ID 100: 可能需要滚动 ${Math.ceil((minId - 100) / 20)} 轮`);
  }

  async run() {
    console.log('📊 帖子数据分析工具');
    console.log('=====================================\n');

    const loaded = await this.loadPosts();
    if (!loaded) return;

    this.analyzePostIds();
    this.estimateTimeRange();
    this.generateReport();
  }
}

async function main() {
  const analyzer = new PostAnalyzer();
  await analyzer.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = PostAnalyzer;
