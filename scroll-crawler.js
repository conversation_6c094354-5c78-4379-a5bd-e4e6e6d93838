// 无限滚动爬虫 - 专门针对滚动加载的网站
const puppeteer = require('puppeteer');
const fs = require('fs-extra');
const config = require('./src/config');

class ScrollCrawler {
  constructor() {
    this.browser = null;
    this.page = null;
    this.allPosts = [];
    this.seenUrls = new Set();
  }

  // 初始化浏览器
  async init() {
    console.log('🚀 启动浏览器...');

    const chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
    ];

    let executablePath = null;
    const fs_sync = require('fs');
    for (const path of chromePaths) {
      if (fs_sync.existsSync(path)) {
        executablePath = path;
        break;
      }
    }

    const launchOptions = {
      headless: false,  // 显示浏览器便于观察滚动过程
      slowMo: 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    };

    if (executablePath) {
      launchOptions.executablePath = executablePath;
    }

    this.browser = await puppeteer.launch(launchOptions);
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1366, height: 768 });
  }

  // 加载cookies
  async loadCookies() {
    try {
      const cookiesPath = './cookies/cookies.json';
      if (await fs.pathExists(cookiesPath)) {
        const cookies = await fs.readJson(cookiesPath);
        await this.page.setCookie(...cookies);
        console.log('🍪 Cookies已加载');
        return true;
      }
    } catch (error) {
      console.error('加载cookies失败:', error.message);
    }
    return false;
  }

  // 检查登录状态
  async checkLoginStatus() {
    try {
      await this.page.goto('https://sns.vip.stockstar.com/sns', { 
        waitUntil: 'networkidle2' 
      });

      const isLoggedIn = await this.page.evaluate(() => {
        return !document.body.textContent.includes('登录') || 
               document.body.textContent.includes('退出');
      });

      if (!isLoggedIn) {
        console.log('🔐 需要登录，请在浏览器中手动登录...');
        console.log('登录完成后，按回车键继续...');
        
        await new Promise(resolve => {
          process.stdin.once('data', () => resolve());
        });

        const cookies = await this.page.cookies();
        await fs.ensureDir('./cookies');
        await fs.writeJson('./cookies/cookies.json', cookies);
        console.log('✅ 登录状态已保存');
      } else {
        console.log('✅ 已登录状态');
      }

      return true;
    } catch (error) {
      console.error('❌ 登录检查失败:', error.message);
      return false;
    }
  }

  // 获取当前页面的帖子数量
  async getCurrentPostCount() {
    try {
      const count = await this.page.evaluate(() => {
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        return links.length;
      });
      return count;
    } catch (error) {
      return 0;
    }
  }

  // 提取当前页面的所有帖子
  async extractAllPosts() {
    try {
      const posts = await this.page.evaluate(() => {
        const results = [];
        const links = document.querySelectorAll('a[href*="forumdetail"]');
        
        links.forEach(link => {
          const title = link.textContent.trim();
          const url = link.href;
          
          if (title && title.length > 3 && url) {
            results.push({ title, url });
          }
        });

        return results;
      });

      // 去重并添加到总列表
      let newPostsCount = 0;
      posts.forEach(post => {
        if (!this.seenUrls.has(post.url)) {
          this.seenUrls.add(post.url);
          this.allPosts.push({
            id: this.allPosts.length + 1,
            title: post.title,
            url: post.url
          });
          newPostsCount++;
        }
      });

      return newPostsCount;
    } catch (error) {
      console.error('❌ 提取帖子失败:', error.message);
      return 0;
    }
  }

  // 智能滚动加载所有内容
  async scrollToLoadAll() {
    try {
      console.log('📜 开始无限滚动加载所有帖子...\n');
      
      let scrollAttempts = 0;
      let consecutiveNoNewPosts = 0;
      const maxScrollAttempts = 200;  // 最大滚动次数
      const maxConsecutiveNoNew = 10; // 连续多少次没有新帖子就停止
      
      let lastPostCount = 0;
      
      while (scrollAttempts < maxScrollAttempts && consecutiveNoNewPosts < maxConsecutiveNoNew) {
        scrollAttempts++;
        
        // 获取滚动前的帖子数量
        const beforeCount = await this.getCurrentPostCount();
        
        // 多种滚动策略
        await this.page.evaluate(() => {
          // 方法1: 滚动到页面底部
          window.scrollTo(0, document.body.scrollHeight);
        });

        // 等待一下
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 方法2: 模拟用户滚动
        await this.page.evaluate(() => {
          // 先滚动到底部
          window.scrollTo(0, document.body.scrollHeight);

          // 然后稍微往上滚一点，再滚到底部（模拟用户行为）
          setTimeout(() => {
            window.scrollBy(0, -100);
            setTimeout(() => {
              window.scrollTo(0, document.body.scrollHeight);
            }, 200);
          }, 500);
        });

        // 等待内容加载（增加等待时间）
        await new Promise(resolve => setTimeout(resolve, 4000));
        
        // 获取滚动后的帖子数量
        const afterCount = await this.getCurrentPostCount();
        
        // 提取新帖子
        const newPostsCount = await this.extractAllPosts();
        
        if (newPostsCount > 0) {
          consecutiveNoNewPosts = 0;
          console.log(`📜 第${scrollAttempts}次滚动: 发现 ${newPostsCount} 个新帖子 (总计: ${this.allPosts.length})`);
        } else {
          consecutiveNoNewPosts++;
          console.log(`📜 第${scrollAttempts}次滚动: 没有新帖子 (连续无新帖: ${consecutiveNoNewPosts})`);
        }
        
        // 检查页面高度是否还在变化
        const currentHeight = await this.page.evaluate(() => document.body.scrollHeight);
        
        // 如果页面高度没有变化且没有新帖子，可能已经到底了
        if (afterCount === beforeCount && newPostsCount === 0) {
          console.log('📄 页面内容没有变化，可能已经加载完所有内容');
          
          // 再尝试几次确认
          for (let i = 0; i < 3; i++) {
            await this.page.evaluate(() => {
              window.scrollTo(0, document.body.scrollHeight);
            });
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const finalNewPosts = await this.extractAllPosts();
            if (finalNewPosts > 0) {
              console.log(`📜 最终确认发现 ${finalNewPosts} 个新帖子`);
              consecutiveNoNewPosts = 0;
              break;
            }
          }
          
          if (consecutiveNoNewPosts >= maxConsecutiveNoNew) {
            break;
          }
        }
        
        // 每20次滚动保存一次进度
        if (scrollAttempts % 20 === 0) {
          await this.saveProgress();
          console.log(`💾 已保存进度 (第${scrollAttempts}次滚动, ${this.allPosts.length}个帖子)`);
        }
        
        // 随机延迟，避免被检测
        const delay = Math.random() * 1000 + 500;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      console.log(`\n🎉 滚动加载完成！`);
      console.log(`📊 总共滚动 ${scrollAttempts} 次`);
      console.log(`📊 获取到 ${this.allPosts.length} 个不重复的帖子`);
      
      return this.allPosts;
      
    } catch (error) {
      console.error('❌ 滚动加载失败:', error.message);
      return this.allPosts;
    }
  }

  // 保存进度
  async saveProgress() {
    try {
      await fs.ensureDir('./data');
      const progressFile = './data/scroll_progress.json';
      await fs.writeJson(progressFile, {
        timestamp: new Date().toISOString(),
        totalPosts: this.allPosts.length,
        posts: this.allPosts
      }, { spaces: 2 });
    } catch (error) {
      console.error('保存进度失败:', error.message);
    }
  }

  // 保存最终结果
  async saveResults() {
    try {
      await fs.ensureDir('./data');
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const jsonFile = `./data/all_posts_scroll_${timestamp}.json`;
      const txtFile = `./data/all_posts_scroll_${timestamp}.txt`;
      
      // 保存JSON格式
      await fs.writeJson(jsonFile, this.allPosts, { spaces: 2 });
      
      // 保存文本格式
      const textContent = this.allPosts.map((post, index) => 
        `${index + 1}. ${post.title}\n   ${post.url}`
      ).join('\n\n');
      
      await fs.writeFile(txtFile, textContent, 'utf8');
      
      console.log(`💾 结果已保存:`);
      console.log(`   JSON: ${jsonFile}`);
      console.log(`   文本: ${txtFile}`);
      
      return jsonFile;
    } catch (error) {
      console.error('❌ 保存结果失败:', error.message);
    }
  }

  // 主执行函数
  async run() {
    try {
      await this.init();
      await this.loadCookies();
      
      const loginSuccess = await this.checkLoginStatus();
      if (!loginSuccess) {
        console.log('❌ 登录失败，退出程序');
        return;
      }

      const posts = await this.scrollToLoadAll();
      
      if (posts.length > 0) {
        await this.saveResults();
        
        console.log('\n📋 爬取结果预览:');
        posts.slice(0, 10).forEach((post, index) => {
          console.log(`${index + 1}. ${post.title}`);
        });
        
        if (posts.length > 10) {
          console.log(`... 还有 ${posts.length - 10} 个帖子`);
        }
        
        console.log(`\n🎯 爬取统计:`);
        console.log(`📊 总帖子数: ${posts.length}`);
        console.log(`🔗 所有帖子都包含标题和链接`);
        
      } else {
        console.log('❌ 未获取到任何帖子');
      }

    } catch (error) {
      console.error('❌ 程序执行失败:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
        console.log('🔚 浏览器已关闭');
      }
    }
  }
}

// 主函数
async function main() {
  console.log('🎯 无限滚动帖子爬虫');
  console.log('=====================================\n');
  console.log('📝 说明: 这个爬虫会持续滚动页面直到加载完所有帖子');
  console.log('⏱️ 预计需要几分钟时间，请耐心等待...\n');
  
  const crawler = new ScrollCrawler();
  await crawler.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = ScrollCrawler;
