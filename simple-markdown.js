// 简化版Markdown转换器 - 只处理标题和链接
const fs = require('fs-extra');

class SimpleMarkdownConverter {
  constructor() {
    this.outputDir = './simple-output';
  }

  // 初始化输出目录
  async init() {
    await fs.ensureDir(this.outputDir);
    console.log('📁 输出目录已创建');
  }

  // 生成简单的Markdown索引
  async generateSimpleIndex(posts) {
    try {
      let markdownContent = `# 证券之星论坛帖子列表\n\n`;
      markdownContent += `> 生成时间: ${new Date().toLocaleString('zh-CN')}\n`;
      markdownContent += `> 总帖子数: ${posts.length}\n\n`;

      // 生成简单的编号列表
      posts.forEach((post, index) => {
        markdownContent += `${index + 1}. [${post.title}](${post.url})\n`;
      });

      markdownContent += `\n---\n\n`;
      markdownContent += `**说明**: 点击标题可直接访问原帖\n`;

      // 保存文件
      const outputFile = `${this.outputDir}/帖子列表.md`;
      await fs.writeFile(outputFile, markdownContent, 'utf8');
      
      console.log(`✅ Markdown列表已生成: ${outputFile}`);
      return outputFile;

    } catch (error) {
      console.error('❌ 生成Markdown失败:', error.message);
    }
  }

  // 生成表格格式的Markdown
  async generateTableFormat(posts) {
    try {
      let markdownContent = `# 证券之星论坛帖子列表（表格格式）\n\n`;
      markdownContent += `> 生成时间: ${new Date().toLocaleString('zh-CN')}\n`;
      markdownContent += `> 总帖子数: ${posts.length}\n\n`;

      // 表格头
      markdownContent += `| 序号 | 帖子标题 | 链接 |\n`;
      markdownContent += `|------|----------|------|\n`;

      // 表格内容
      posts.forEach((post, index) => {
        const title = post.title.replace(/\|/g, '\\|'); // 转义表格中的竖线
        markdownContent += `| ${index + 1} | ${title} | [查看原文](${post.url}) |\n`;
      });

      // 保存文件
      const outputFile = `${this.outputDir}/帖子列表_表格.md`;
      await fs.writeFile(outputFile, markdownContent, 'utf8');
      
      console.log(`✅ 表格格式已生成: ${outputFile}`);
      return outputFile;

    } catch (error) {
      console.error('❌ 生成表格格式失败:', error.message);
    }
  }

  // 主处理函数
  async convert(dataFile) {
    try {
      await this.init();
      
      console.log('🚀 开始转换为Markdown...\n');
      
      // 读取数据文件
      if (!await fs.pathExists(dataFile)) {
        console.error(`❌ 数据文件不存在: ${dataFile}`);
        return;
      }

      const posts = await fs.readJson(dataFile);
      console.log(`📊 共找到 ${posts.length} 个帖子`);

      if (posts.length === 0) {
        console.log('❌ 没有数据可转换');
        return;
      }

      // 生成两种格式
      await this.generateSimpleIndex(posts);
      await this.generateTableFormat(posts);

      console.log(`\n🎉 转换完成!`);
      console.log(`📁 输出目录: ${this.outputDir}`);

    } catch (error) {
      console.error('❌ 转换失败:', error.message);
    }
  }
}

// 主函数
async function main() {
  const converter = new SimpleMarkdownConverter();
  
  // 检查数据文件
  const dataFile = './data/post_list.json';
  
  if (!await fs.pathExists(dataFile)) {
    console.error('❌ 未找到数据文件，请先运行爬虫：node simple-crawler.js');
    return;
  }
  
  console.log(`📖 使用数据文件: ${dataFile}`);
  await converter.convert(dataFile);
}

// 如果直接运行此脚本
if (require.main === module) {
  console.log('🎯 简化版Markdown转换器');
  console.log('=====================================\n');
  
  main().catch(console.error);
}

module.exports = SimpleMarkdownConverter;
